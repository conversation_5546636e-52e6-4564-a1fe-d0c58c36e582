"""
filer_paths.py

Module to handle filer paths.
"""

from __future__ import absolute_import
from builtins import str
import os
from typing import Optional, Union
from elipy2 import core, frostbite_core, local_paths, SETTINGS


def get_build_share_path(location=None, target_build_share=None):
    """
    Get a normalized build_share path
    :param location: The location in the elipy config to get the build_share from
    :param target_build_share: alternate_build_shares key if not using classic build_share value
    """
    if target_build_share:
        _build_share = SETTINGS.get("alternate_build_shares", location)[target_build_share]
    else:
        _build_share = SETTINGS.get("build_share", location)
    if core.is_buildsystem_run():
        return _build_share
    else:
        if os.getenv("username") is not None:
            return os.path.join(_build_share, "userbuild", os.getenv("username"))
        else:
            return os.path.join(_build_share, "userbuild", "unknown")


def get_code_build_root_path(
    branch,
    changelist,
    nomaster=False,
    custom_tag=None,
    location=None,
    target_build_share=None,
    stressbulkbuild=False,
):
    """
    Constructs path to the root folder for code/binary builds on network storage.
    Excludes licensee, platform and config.\n
    Usage:
        you need to know the path to a specific
            changelist.
    """
    folder_name = "Code"
    # Store nomaster and stressbulkbuild binaries outside of Code to not pollute
    # content creator tnt/local/autobuild folders.
    if nomaster:
        folder_name += "_nomaster"
    if stressbulkbuild:
        folder_name += "_stressbulkbuild"

    if custom_tag:
        ret = os.path.join(
            get_build_share_path(location=location, target_build_share=target_build_share),
            folder_name,
            branch,
            custom_tag,
            str(changelist),
        )
    else:
        ret = os.path.join(
            get_build_share_path(location=location, target_build_share=target_build_share),
            folder_name,
            branch,
            str(changelist),
        )

    return ret


def get_code_build_path(
    branch,
    changelist,
    platform,
    config,
    custom_tag=None,
    nomaster=False,
    location=None,
    target_build_share=None,
    stressbulkbuild=False,
):
    """
    Constructs path to the full folder for the code/binary builds on network storage
    Includes licensee, platform and config.\n
    Usage:
        you need to know the path to a specific
            platform,
            configuration,
            changelist.
    """
    return os.path.join(
        get_code_build_root_path(
            branch,
            changelist,
            custom_tag=custom_tag,
            nomaster=nomaster,
            location=location,
            target_build_share=target_build_share,
            stressbulkbuild=stressbulkbuild,
        ),
        local_paths.get_platform_path(platform, config),
    )


def get_code_build_platform_path(
    branch,
    changelist,
    platform,
    nomaster=False,
    location=None,
    target_build_share=None,
    custom_tag=None,
    stressbulkbuild=False,
):
    """
    Constructs path to the full folder for the code/binary builds on network storage or azure.
    Includes licensee, platform.\n
    Parameters:
        branch: branch name
        changelist: changelist number
        platform: platform name
        nomaster: flag to indicate if the build is a nomaster build
        location: location in the elipy config to get the build_share from
        target_build_share: alternate_build_shares key if not using classic build_share value
        custom_tag: custom tag to append to the path
    Usage:
        you need to know the path to a specific
            platform
    """
    return os.path.join(
        get_code_build_root_path(
            branch,
            changelist,
            custom_tag=custom_tag,
            nomaster=nomaster,
            location=location,
            target_build_share=target_build_share,
            stressbulkbuild=stressbulkbuild,
        ),
        local_paths.get_platform_path(platform, config=None),
    )


def get_binlog_build_path(
    branch,
    changelist,
    platform=None,
    config=None,
    nomaster=False,
    meta_path=False,
    stressbulkbuild=False,
):
    """
    Constructs path to the full folder for the code/binary builds on a network storage
    but to a specific meta location for the binlog files.
    meta_path returns the path to the meta directory to help add the needed
    {platform}.locked file to stop nicesyncer picking up the platform folders there.
    Usage:
        You need to know the path to a specific
            platform,
            configuration,
            changelist,
            branch
        However only branch, changelist are needed for meta_path.
    """
    if meta_path:
        return os.path.join(
            get_code_build_root_path(
                branch, changelist, nomaster=nomaster, stressbulkbuild=stressbulkbuild
            ),
            "meta",
        )
    return os.path.join(
        get_code_build_root_path(
            branch, changelist, nomaster=nomaster, stressbulkbuild=stressbulkbuild
        ),
        "meta",
        platform,
        config,
    )


def get_tnt_local_build_root_path(branch, changelist):
    """
    Constructs path to the root folder for code/binary builds on network storage.
    Excludes licensee, platform and config.\n
    Usage:
        you need to know the path to a specific
            changelist.
    """
    return os.path.join(get_build_share_path(), "tnt_local", branch, str(changelist))


def get_tnt_local_build_path(
    branch, changelist, platform, config, nomaster=False, stressbulkbuild=False
):
    """
    Constructs path to the root folder for code/binary builds on network storage.
    Excludes licensee, platform and config.\n
    Usage:
        you need to know the path to a specific
            changelist.
    """
    if nomaster:
        platform = platform + "_nomaster"
    if stressbulkbuild:
        platform += "_stressbulkbuild"
    return os.path.join(get_tnt_local_build_root_path(branch, changelist), platform, config)


def get_ant_local_build_path(branch, changelist):
    """
    Constructs path to the root folder for code/binary builds on network storage.
    Excludes licensee, platform and config.\n
    Usage:
        you need to know the path to a specific
            changelist.
    """
    return os.path.join(get_build_share_path(), "ant_cache", branch, str(changelist))


def get_offsite_build_location(branch):
    """
    Constructs path to the top level folder for a branch for the offsite builds on network
    storage.\n
    Usage:
        find out where to ship branch offsite
    """
    return os.path.join(get_build_share_path(), "offsite", branch)


# pylint: disable=invalid-name
def get_offsite_basic_build_location(branch):
    """
    Constructs path to the top level folder for a branch for the offsite basic builds on network
    storage.\n
    Usage:
        find out where to ship branch offsite basic
         - basic in this sense is to say it's a cut down version, with only the minimum
           requirements to run drone
    """
    return os.path.join(get_build_share_path(), "offsite_basic", branch)


# pylint: disable=invalid-name
def get_offsite_basic_drone_build_location(branch):
    """
    Constructs path to the top level folder for a branch for the offsite basic drone builds on
    network storage.\n
    Usage:
        find out where to ship branch offsite basic drone
         - basic in this sense is to say it's a cut down version, with only the minimum
           requirements to run drone
    """
    return os.path.join(get_build_share_path(), "offsite_basic_drone", branch)


def get_offsite_build(branch, changelist):
    """
    Constructs full path for the offsite builds on network storage.\n
    Usage:
        find out where to ship branch/changelist/changelist.zip offsite
    """
    return os.path.join(
        get_offsite_build_location(branch), str(changelist), str(changelist) + ".zip"
    )


def get_offsite_basic_build(branch, changelist):
    """
    Constructs full path for the offsite basic builds on network storage.\n
    Usage:
        find out where to ship branch/changelist.zip offsite basic
         - basic in this sense is to say it's a cut down version, with only the minimum
           requirements to run drone
    """
    return os.path.join(get_offsite_basic_build_location(branch), str(changelist) + ".zip")


def get_offsite_basic_drone_build(branch, changelist):
    """
    Constructs full path for the offsite basic drone zip builds on network storage.\n
    Usage:
        find out where to ship branch/changelist/changelist.zip offsite basic drone
         - basic in this sense is to say it's a cut down version, with only the minimum
           requirements to run drone
         - This is used either by other EA teams or external/3rd party vendors
    """
    return os.path.join(
        get_offsite_basic_drone_build_location(branch),
        str(changelist),
        str(changelist) + ".zip",
    )


def get_outsourcer_build_location(outsourcer, branch):
    """
    Constructs path to the top level folder for the outsourcer builds on the network storage.\n
    Usage:
        find out the location for outsourcer builds for a branch (all CLs).
         - This is the basic drone setup. Basic in this sense is to say it's a cut down version,
           with only the minimum requirements to run drone.
         - This is used by external/3rd party vendors, e.g. Jukebox machines.
    """

    return os.path.join(get_build_share_path(), outsourcer, branch)


def get_outsourcer_build(outsourcer, branch, changelist):
    """
    Constructs path to the full folder for one outsourcer build on the network storage.\n
    Usage:
        find out the location for a specific outsourcer build (branch/CL).
         - This is the basic drone setup. Basic in this sense is to say it's a cut down version,
           with only the minimum requirements to run drone.
         - This is used by external/3rd party vendors, e.g. Jukebox machines.
    """

    return os.path.join(get_outsourcer_build_location(outsourcer, branch), str(changelist))


def get_baseline_build_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    location: Union[str, None] = None,
):
    """
    Constructs path to deployed baseline artifacts on network storage.
    """
    return os.path.join(
        get_build_share_path(location=location),
        "baselines",
        frostbite_core.get_licensee_id(),
        data_branch,
        data_changelist,
        code_branch,
        code_changelist,
    )


def get_delta_bundles_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
):
    """
    Returns path to delta bundles for a Frosty build.
    """
    return os.path.join(
        get_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
        ),
        "delta",
    )


def get_state_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
):
    """
    Returns path to state for a Frosty build.
    """
    return os.path.join(
        get_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
        ),
        "state",
    )


def get_head_bundles_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
    combine_data_branch: Optional[str] = None,
    combine_data_changelist: Optional[str] = None,
    combine_code_branch: Optional[str] = None,
    combine_code_changelist: Optional[str] = None,
) -> str:
    """
    Returns path to head bundles for a Frosty build.
    If combine parameters are provided, returns path to combined bundles.
    """
    if (
        combine_data_branch
        and combine_data_changelist
        and combine_code_branch
        and combine_code_changelist
    ):
        # Return path for combined bundles with folder separation
        base_path = get_frosty_base_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
        )

        combined_path = os.path.join(
            base_path,
            "combined_bundles",
            code_branch,
            str(code_changelist),
            data_branch,
            str(data_changelist),
            combine_code_branch,
            str(combine_code_changelist),
            combine_data_branch,
            str(combine_data_changelist),
        )

        return combined_path
    else:
        # Return regular head bundles path
        return os.path.join(
            get_bundles_path(
                data_branch=data_branch,
                data_changelist=data_changelist,
                code_branch=code_branch,
                code_changelist=code_changelist,
                platform=platform,
                bundles_dir_name=bundles_dir_name,
            ),
            "head",
        )


def get_bundles_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
) -> str:
    """
    Returns path to head bundles for a Frosty build.
    """
    return os.path.join(
        get_frosty_base_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
        ),
        bundles_dir_name,
    )


def get_baseline_delta_bundles_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
):
    """
    Returns path to delta bundles for a baseline build.
    """
    return os.path.join(
        get_baseline_build_path(data_branch, data_changelist, code_branch, code_changelist),
        platform,
        bundles_dir_name,
        "delta",
    )


def get_baseline_head_bundles_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
):
    """
    Returns path to head bundles for a baseline build.
    """
    return os.path.join(
        get_baseline_build_path(data_branch, data_changelist, code_branch, code_changelist),
        platform,
        bundles_dir_name,
        "head",
    )


def get_baseline_state_path(
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
):
    """
    Returns path to state for a baseline build.
    """
    return os.path.join(
        get_baseline_build_path(data_branch, data_changelist, code_branch, code_changelist),
        platform,
        bundles_dir_name,
        "state",
    )


def get_frosty_base_path(
    data_branch=None, data_changelist=None, code_branch=None, code_changelist=None
):
    """
    Constructs base path to packaged (frosty) builds on network storage.

    Does not include platform package type, region, config.
    Useful if you want all frosty builds related to the changelists
    """
    return os.path.join(
        get_build_share_path(),
        "frosty",
        frostbite_core.get_licensee_id(),
        data_branch,
        str(data_changelist),
        code_branch,
        str(code_changelist),
    )


def get_frosty_base_build_path(
    data_branch=None,
    data_changelist=None,
    code_branch=None,
    code_changelist=None,
    platform=None,
    location=None,
):
    """
    Constructs base path to packaged (frosty) builds on network storage.

    Does not include package type, region, config.
    Useful if you want to know where all artefacts for a build would be (state, bundles).
    """
    return os.path.join(
        get_build_share_path(location=location),
        "frosty",
        frostbite_core.get_licensee_id(),
        data_branch,
        str(data_changelist),
        code_branch,
        str(code_changelist),
        platform,
    )


def get_frosty_build_path(
    data_branch=None,
    data_changelist=None,
    code_branch=None,
    code_changelist=None,
    platform=None,
    package_type=None,
    region="WW",
    config=None,
    location=None,
    combine_data_branch=None,
    combine_data_changelist=None,
    combine_code_branch=None,
    combine_code_changelist=None,
    content_layer=None,
):
    """
    Constructs full path to packaged (frosty) builds on network storage.
    """
    base_path = get_frosty_base_build_path(
        data_branch,
        data_changelist,
        code_branch,
        code_changelist,
        platform,
        location=location,
    )

    build_path = [base_path]

    if content_layer:
        build_path.append(f"ContentLayer_{content_layer}")

    build_path.extend([package_type, region, config])

    if combine_data_branch and combine_code_branch:
        build_path.extend(
            [
                combine_data_branch,
                combine_data_changelist,
                combine_code_branch,
                combine_code_changelist,
            ]
        )

    return os.path.join(*build_path)


def get_symbol_path(branch=None, changelist=None, platform=None, config=None):
    """
    Constructs full path to symbols on network storage.
    """
    if platform.lower() == "win64":
        platform = "win64game"
    return os.path.join(
        get_build_share_path(),
        "symbols",
        branch,
        str(changelist),
        local_paths.get_platform_path(platform, config),
    )


def get_avalanche_export_path(branch, platform, data_changelist, code_changelist):
    """
    Returns the filer path to an exported avalanche state
    """
    return os.path.join(
        get_build_share_path(),
        "AvalancheState",
        branch,
        platform,
        data_changelist,
        code_changelist,
    )


def get_expression_debug_data_path(
    data_branch, data_changelist, code_branch, code_changelist, platform
):
    """
    Return filer path where expression debug data stores
    """
    return os.path.join(
        get_build_share_path(),
        "ExpressionDebugData",
        frostbite_core.get_licensee_id(),
        data_branch,
        data_changelist,
        code_branch,
        code_changelist,
        platform,
    )


def get_shift_subscription_download_path(build_type, branch):
    """
    Return filer path where builds are downloaded to if we have a Shift subscription.
    """
    if build_type == "code":
        return os.path.join(
            get_build_share_path(), "ShiftSubscriptionDownloads", build_type, branch
        )
    else:
        return os.path.join(
            get_build_share_path(target_build_share=build_type),
            "ShiftSubscriptionDownloads",
            build_type,
            branch,
        )


def get_shift_processing_target_path(build_type, branch, changelist):
    """
    Return filer path where the output from the shift download processing job will be stored.
    """
    return os.path.join(get_build_share_path(target_build_share=build_type), branch, changelist)
